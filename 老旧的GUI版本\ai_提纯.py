import os
import re
import time
import json
import openai
from typing import List, Dict, Any, Optional, Union

class AI提纯器:
    def __init__(self):
        self.api_key = self._加载API密钥()
        self.提纯数据目录 = os.path.dirname(os.path.abspath(__file__))
        
    def 设置提纯数据目录(self, 提纯数据目录: str) -> None:
        """设置提纯数据保存目录"""
        self.提纯数据目录 = 提纯数据目录
        
    def _加载API密钥(self) -> str:
        """从文件加载API密钥"""
        api_key = ""
        api_keys_file = "api_keys.txt"
        
        try:
            if os.path.exists(api_keys_file):
                with open(api_keys_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip() and not line.strip().startswith('#'):
                            api_key = line.strip()
                            break
        except Exception as e:
            print(f"加载API密钥时出错: {e}")
            
        return api_key
    
    def 提纯游戏名称(self, 标题列表: List[str]) -> List[Dict]:
        """使用AI提取游戏名称"""
        if not self.api_key:
            print("未设置API密钥，无法使用AI提纯功能")
            return []
            
        openai.api_key = self.api_key
        
        # 分批处理，避免超出token限制
        批次大小 = 20
        结果列表 = []
        
        for i in range(0, len(标题列表), 批次大小):
            批次标题 = 标题列表[i:i+批次大小]
            
            try:
                print(f"正在处理第 {i//批次大小 + 1} 批，共 {len(批次标题)} 个标题")
                批次结果 = self._处理单批次(批次标题)
                结果列表.extend(批次结果)
                
                # 避免API请求过于频繁
                if i + 批次大小 < len(标题列表):
                    time.sleep(1)
                    
            except Exception as e:
                print(f"处理批次 {i//批次大小 + 1} 时出错: {e}")
                
        # 保存提纯结果
        self.保存提纯结果(结果列表)
        
        return 结果列表
    
    def _处理单批次(self, 标题列表: List[str]) -> List[Dict]:
        """处理单个批次的标题"""
        提示词 = self._构建提示词(标题列表)
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个专业的游戏名称提取助手。你的任务是从闲鱼商品标题中提取出准确的游戏名称。"},
                    {"role": "user", "content": 提示词}
                ],
                temperature=0.2,
                max_tokens=2000
            )
            
            回复内容 = response.choices[0].message.content
            return self._解析AI回复(回复内容, 标题列表)
            
        except Exception as e:
            print(f"调用API时出错: {e}")
            return []
    
    def _构建提示词(self, 标题列表: List[str]) -> str:
        """构建AI提示词"""
        提示词 = "我需要你从以下闲鱼商品标题中提取出准确的游戏名称。\n\n"
        提示词 += "要求：\n"
        提示词 += "1. 只提取游戏名称，不要包含其他信息（如平台、价格、状态等）\n"
        提示词 += "2. 如果无法确定游戏名称，请回复'未知'\n"
        提示词 += "3. 请以JSON格式返回，格式为：[{\"原标题\": \"标题1\", \"游戏名称\": \"提取的游戏名称1\"}, ...]\n\n"
        提示词 += "以下是需要处理的商品标题：\n"
        
        for i, 标题 in enumerate(标题列表):
            提示词 += f"{i+1}. {标题}\n"
            
        return 提示词
    
    def _解析AI回复(self, 回复内容: str, 原标题列表: List[str]) -> List[Dict]:
        """解析AI的回复内容"""
        结果列表 = []
        
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'\[.*\]', 回复内容, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                结果 = json.loads(json_str)
                
                # 确保结果与原标题列表长度一致
                if len(结果) != len(原标题列表):
                    print(f"警告：AI返回的结果数量 ({len(结果)}) 与原标题数量 ({len(原标题列表)}) 不一致")
                
                return 结果
            else:
                print("无法从AI回复中提取JSON数据")
                
        except json.JSONDecodeError:
            print("解析JSON数据时出错")
        except Exception as e:
            print(f"解析AI回复时出错: {e}")
            
        # 如果解析失败，返回原标题和未知游戏名称
        for 标题 in 原标题列表:
            结果列表.append({
                "原标题": 标题,
                "游戏名称": "未知"
            })
            
        return 结果列表
    
    def 保存提纯结果(self, 结果列表: List[Dict]) -> None:
        """保存提纯结果到文件"""
        if not 结果列表:
            return
            
        # 保存完整JSON结果
        json_文件名 = os.path.join(self.提纯数据目录, f"提纯结果_{time.strftime('%Y%m%d%H%M%S')}.json")
        try:
            with open(json_文件名, 'w', encoding='utf-8') as f:
                json.dump(结果列表, f, ensure_ascii=False, indent=2)
            print(f"提纯结果已保存到: {json_文件名}")
        except Exception as e:
            print(f"保存提纯JSON结果时出错: {e}")
            
        # 保存纯文本游戏名称列表
        txt_文件名 = os.path.join(self.提纯数据目录, "提纯游戏名字.txt")
        try:
            游戏名称集合 = set()
            for 结果 in 结果列表:
                游戏名称 = 结果.get("游戏名称", "").strip()
                if 游戏名称 and 游戏名称 != "未知":
                    游戏名称集合.add(游戏名称)
                    
            with open(txt_文件名, 'w', encoding='utf-8') as f:
                for 游戏名称 in sorted(游戏名称集合):
                    f.write(f"{游戏名称}\n")
                    
            print(f"游戏名称列表已保存到: {txt_文件名}")
        except Exception as e:
            print(f"保存游戏名称列表时出错: {e}")
    
    def 加载提纯结果(self, 文件路径: str) -> List[Dict]:
        """从文件加载提纯结果"""
        try:
            with open(文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载提纯结果时出错: {e}")
            return [] 